use chrono::{
    NaiveDate,
    NaiveTime,
    Datelike,
};
use regex::Regex;

pub fn normalize_date(date: &str) -> String {
    // first try parsing 2-digit year format with manual fix
    if let Ok(parsed) = NaiveDate::parse_from_str(date, "%m/%d/%y") {
        let year = parsed.year();
        let fixed_year = if year < 100 {
            if year >= 70 { 1900 + year } else { 2000 + year }
        } else {
            year
        };

        if let Some(fixed_date) = NaiveDate::from_ymd_opt(fixed_year, parsed.month(), parsed.day()) {
            return fixed_date.format("%Y-%m-%d").to_string();
        }
    }

    // then try parsing 4-digit year normally
    if let Ok(parsed) = NaiveDate::parse_from_str(date, "%m/%d/%Y") {
        return parsed.format("%Y-%m-%d").to_string();
    }

    // if both fail, return original
    date.to_string()
}


pub fn normalize_dns_question_name(url: &str) -> String {
    let re = Regex::new(r"\([0-9]+\)").unwrap();
    let normalized = re.replace_all(url, ".");
    normalized.trim_matches('.').to_string()
}


fn normalize_time(time: &str) -> String {
    // Try parsing with 12-hour format with AM/PM (%I:%M:%S %p)
    if let Ok(t) = NaiveTime::parse_from_str(time, "%I:%M:%S %p") {
        return t.format("%H:%M:%S").to_string();
    }

    // Try parsing with 24-hour format with AM/PM (%H:%M:%S %p)
    if let Ok(t) = NaiveTime::parse_from_str(time, "%H:%M:%S %p") {
        return t.format("%H:%M:%S").to_string();
    }

    // If both parsing attempts fail, return the original string
    time.to_string()
}

// tests ----------------------

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_normalize_date() {
        // MM/DD/YYYY format
        assert_eq!(normalize_date("12/8/2020"),  "2020-12-08");
        assert_eq!(normalize_date("12/31/2020"), "2020-12-31");
        assert_eq!(normalize_date("01/01/2000"), "2000-01-01");
        assert_eq!(normalize_date("12/31/1999"), "1999-12-31");
        
        // MM/DD/YY format
        assert_eq!(normalize_date("12/8/20"),  "2020-12-08");
        assert_eq!(normalize_date("12/31/20"), "2020-12-31");
        assert_eq!(normalize_date("01/01/00"), "2000-01-01");
        assert_eq!(normalize_date("12/31/99"), "1999-12-31");

        // invalid format
        assert_eq!(normalize_date("31/12/2020"),   "31/12/2020");
        assert_eq!(normalize_date("2020-12-31"),   "2020-12-31");
        assert_eq!(normalize_date("invalid date"), "invalid date");
        assert_eq!(normalize_date(""),             "");
    }

    #[test]
    fn test_normalize_dns_question_name() {
        assert_eq!(normalize_dns_question_name("example(123)com"),              "example.com");
        assert_eq!(normalize_dns_question_name("(456)example(789)com"),         "example.com");
        assert_eq!(normalize_dns_question_name("example(123)com(456)"),         "example.com");
        assert_eq!(normalize_dns_question_name("sub(1)domain(2)example(3)com"), "sub.domain.example.com");
        assert_eq!(normalize_dns_question_name("example.com"),                  "example.com");
        assert_eq!(normalize_dns_question_name(""),                             "");
        assert_eq!(normalize_dns_question_name("(123)(456)(789)"),              "");
    }

    #[test]
    fn test_normalize_time() {
        assert_eq!(normalize_time("12:00:00 AM"), "00:00:00");
        assert_eq!(normalize_time("01:30:45 AM"), "01:30:45");
        assert_eq!(normalize_time("11:59:59 AM"), "11:59:59");
        assert_eq!(normalize_time("12:00:00 PM"), "12:00:00");
        assert_eq!(normalize_time("01:30:45 PM"), "13:30:45");
        assert_eq!(normalize_time("11:59:59 PM"), "23:59:59");
        assert_eq!(normalize_time("1:15:30 AM"),  "01:15:30");
        assert_eq!(normalize_time("9:45:00 PM"),  "21:45:00");
        assert_eq!(normalize_time("00:00:00 AM"), "00:00:00");
        assert_eq!(normalize_time("13:30:45 PM"), "13:30:45");

        // invalid format
        assert_eq!(normalize_time("25:00:00 PM"), "25:00:00 PM");
        assert_eq!(normalize_time("12:60:00 AM"), "12:60:00 AM");
        assert_eq!(normalize_time("not a time"),  "not a time");
        assert_eq!(normalize_time("12:00:00"),    "12:00:00");
    }
}
