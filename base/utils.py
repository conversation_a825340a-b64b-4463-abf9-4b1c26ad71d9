from django.conf import settings
from django.core.paginator import Paginator
from django.http import HttpRequest
from django.templatetags.static import static
from django.utils.safestring import mark_safe

from calendar import monthrange
from collections import Counter, defaultdict
from datetime import date, datetime, timedelta
from functools import lru_cache
from inspect import currentframe
from itertools import islice, chain
from json import loads, load
from os import path, listdir, SEEK_END
from pathlib import Path
from random import choice
from re import match, sub, compile as re_compile, fullmatch, escape as re_escape, IGNORECASE
from shutil import get_terminal_size
from subprocess import run
from typing import Tuple, Union, List, Dict, Any
from urllib.parse import quote, unquote  ## urlparse, urlunparse

import httpx

from natsort import natsorted
from rahavard import (
    YMD_REGEX,
    HMS_REGEX,
    colorize,
    convert_byte,
    convert_string_True_False_None_0,
    get_list_of_files,
    is_ymd,
    sort_dict,
    to_tilda,
)

from .utils_ip import (
    is_private,
)

from .utils_classes import (
    GeoLocationConfig,
    MaliciousConfig,
    MYSQLConfig,
)

from .utils_constants import (
    BINARY_PATHS,
    HTTP_HEADERS,
    LAST_LINES,
    LIMITS,
    LOGICAL_OPERATORS,
    LRU_CACHE_MAXSIZE,
    MONTHS_LIST,
    ON_TRUE,
    PICK_AN_OPTION,
    RECENTS_TO_SHOW,
    REFRESHES,
    SEARCH_SIGNS,
    SEASONS_LIST,
    TOPS_TO_SHOW,
)


def paginate(
    rows: Union[List[Any], Dict[Any, Any]],
    limit: int,
    page_number: int,
) -> Union[List[Any], Dict[Any, Any]]:
    '''
    Paginates a list or dictionary of rows.

    Args:
        rows (list or dict): The data to paginate. Can be a list or a dictionary.
        limit (int): The number of items per page.
        page_number (int): The page number to retrieve.

    Returns:
        list or dict: The paginated data. Returns a list if the input was a list,
                      or a dictionary if the input was a dictionary. If the requested
                      page number is beyond the available pages, returns an empty list
                      or dictionary.

    Note:
        - If `rows` is a dictionary, it will be converted to a list of tuples for pagination.
        - If the requested `page_number` is higher than the number of available pages,
          an empty list or dictionary will be returned to prevent infinite querying.
    '''
    ## __HAS_TEST__
    ## __BY_AI__ optimized by copilot

    if isinstance(rows, dict):
        ## dict -> list of tuples
        rows = list(rows.items())
        is_dict = True
    else:
        is_dict = False

    paginator = Paginator(rows, limit)

    ## we are requesting a page number beyond what's inside paginator
    ## we should return empty because
    ## when the page_number is higher than Paginator pages
    ## Paginator returns the last page
    ## meaning this function is always returning sth
    ## no matter what page_number it is provided with
    ## which means htmx tables never reach the end
    ## and keep querying over and over again
    if page_number > paginator.num_pages:
        if is_dict:
            return {}
        return []

    paginated_page = paginator.get_page(page_number)

    if is_dict:
        return dict(paginated_page.object_list)

    return list(paginated_page.object_list)

def all_values_are_0(dictionary: Dict[Any, int]) -> bool:
    '''
    Check if all values in the dictionary are zero.

    Args:
        dictionary (dict): The dictionary to check.

    Returns:
        bool: True if all values are zero, False otherwise.

    Examples:
        >>> all_values_are_0({'a': 0, 'b': 0, 'c': 0})
        True
        >>> all_values_are_0({'a': 0, 'b': 1, 'c': 0})
        False
        >>> all_values_are_0({})
        True
    '''
    ## __HAS_TEST__

    return not any(dictionary.values())

def trim_keys(dictionary: Dict[str, Any], max_key_length: int) -> Dict[str, Any]:
    '''
    Trims the keys of a dictionary to a specified maximum length.

    If a key's length exceeds the maximum length, it is truncated and appended with '...'.

    Args:
        dictionary (dict): The dictionary whose keys are to be trimmed.
        max_key_length (int): The maximum allowed length for the keys.

    Returns:
        dict: A new dictionary with the keys trimmed to the specified maximum length.
    '''
    ## __HAS_TEST__
    ## __BY_AI__ optimized by copilot

    return {
        (k if len(k) <= max_key_length else f'{k[:max_key_length]}...'): v
        for k, v in dictionary.items()
    }

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def move_n_days(ymd: str, n: int) -> str:
    '''
    Move a given date by a specified number of days.

    Args:
        ymd (str): The initial date in 'YYYY-MM-DD' format.
        n (int): The number of days to move the date. Can be positive or negative.

    Returns:
        str: The new date in 'YYYY-MM-DD' format.

    Examples:
        >>> move_n_days('2023-01-01', 5)
        '2023-01-06'

        >>> move_n_days('2023-01-01', -5)
        '2022-12-27'

        >>> move_n_days('2023-12-31', 1)
        '2024-01-01'
    '''
    ## __HAS_TEST__

    date_obj = datetime.strptime(ymd, '%Y-%m-%d')
    return (date_obj + timedelta(days=n)).strftime('%Y-%m-%d')

def get_today_ymd() -> str:
    '''
    Returns the current date in ISO 8601 format (YYYY-MM-DD).

    This function uses the `date.today().isoformat()` method to get the current date
    in the format 'YYYY-MM-DD'.

    Returns:
        str: The current date in ISO 8601 format.
    '''
    ## __HAS_TEST__

    ## method 1
    return date.today().isoformat()

    ## method 2
    ## https://stackoverflow.com/a/47984538/
    # return str(date.today())

    ## method 3 (ditched because it was slower)
    ## return datetime.now().strftime('%Y-%m-%d')

def read_statistics_file(mode: str) -> Dict[str, Any]:
    '''
    Reads a statistics file based on the provided mode and returns its contents.

    Parameters:
    mode (str): The mode indicating which statistics file to read.
                Possible values are 'logs', 'databases', 'parsed-dates', and 'disk-usage'.

    Returns:
        dict: The contents of the statistics file as a dictionary.
              Returns an empty dictionary if the mode is invalid or if an error occurs while reading the file.
    '''
    ## __HAS_TEST__

    if mode == 'logs':
        src_file = settings.LOGS_STATISTICS_FILE
    elif mode == 'databases':
        src_file = settings.DATABASES_STATISTICS_FILE
    elif mode == 'parsed-dates':
        src_file = settings.PARSED_DATES_STATISTICS_FILE
    elif mode == 'disk-usage':
        src_file = settings.DISK_USAGE_STATISTICS_FILE
    else:
        return {}

    try:
        with open(src_file) as opened:
            return load(opened)
    except Exception:
        return {}

def aggregate_values_of_dicts(list_of_dicts: List[Dict[str, int]]) -> Dict[str, int]:
    '''
    Aggregates values of a list of dictionaries by summing the values of common keys.

    Args:
        list_of_dicts (list of dict): A list where each element is a dictionary with keys and numeric values.

    Returns:
        dict: A dictionary with keys from all input dictionaries and their aggregated values, sorted by value in descending order.

    Examples:
        >>> list_of_dicts = [{'a': 1, 'b': 2}, {'a': 3, 'c': 4}]
        >>> aggregate_values_of_dicts(list_of_dicts)
        {'a': 4, 'c': 4, 'b': 2}
    '''
    ## __HAS_TEST__

    agg_dic = Counter()
    for d in list_of_dicts:
        agg_dic.update(d)

    return sort_dict(dict(agg_dic), based_on='value', reverse=True)

def highlight_searched_items_in_db_rows(
    db_rows: List[List[Any]],
    field_headers_and_values: Dict[str, List[str]],
    match_case: bool,
    db_headers_with_indexes: Dict[str, int],
    multi_day_report_allowed: bool,
) -> List[List[Any]]:
    '''
    Highlights searched items in database rows.

    Args:
        db_rows (list of list): The database rows to search and highlight.
        field_headers_and_values (dict): A dictionary where keys are field headers and values are lists of search values.
        match_case (bool): If True, the search will be case-sensitive. If False, the search will be case-insensitive.
        db_headers_with_indexes (dict): A dictionary mapping field headers to their respective column indexes in db_rows.
        multi_day_report_allowed (bool): If True, adjusts the column index to account for multi-day reports.

    Returns:
        list of list: The modified database rows with searched items highlighted.
    '''
    ## __HAS_TEST__

    if not db_rows or not field_headers_and_values:
        return db_rows

    try:
        field_indexes_and_values = defaultdict(list)
        for field_header, field_values in field_headers_and_values.items():
            index = db_headers_with_indexes.get(field_header, None)  ## 5 (int)

            if index is None:
                continue

            ## because rows don't have ID column
            if multi_day_report_allowed:
                index -= 1

            for value in field_values:
                field_indexes_and_values[str(index)].append(
                    ## remove leading/trailing *^$
                    value.strip(f'{SEARCH_SIGNS.asterisk}{SEARCH_SIGNS.caret}{SEARCH_SIGNS.dollar}')
                )

        ## field_indexes_and_values = defaultdict(
        ##     <class 'list'>,
        ##     {
        ##         '5': ['Misc activity'],
        ##         '6': ['3'],
        ##         '8': ['***********', '*********'],
        ##         ...
        ##     }
        ## )

        db_rows__tmp = []
        for row in db_rows:
            row__tmp = []
            for i_idx, item in enumerate(row, start=0):
                for f_idx, values in field_indexes_and_values.items():
                    if not i_idx == int(f_idx):
                        continue
                    for value in values:
                        if match_case:
                            rgx    = re_compile(re_escape(value))
                            item_  = str(item)
                            value_ = value
                        else:
                            rgx    = re_compile(re_escape(value), IGNORECASE)
                            item_  = str(item).lower()
                            value_ = value.lower()

                        if value_ not in item_:
                            continue

                        ## highlight matched word
                        ## (https://stackoverflow.com/a/59245114)
                        item = mark_safe(
                            rgx.sub(
                                lambda _: f'<mark>{_.group()}</mark>',
                                str(item),
                            )
                        )

                row__tmp.append(item)
            db_rows__tmp.append(row__tmp)

        db_rows = db_rows__tmp
    except Exception:
        pass

    return db_rows

def quote_domain(domain: str) -> str:
    '''
    Quotes a domain name by percent-encoding special characters.

    Args:
        domain (str): The domain name to be quoted.

    Returns:
        str: The quoted domain name with special characters percent-encoded.

    Examples:
        >>> quote_domain('example.com')
        'example.com'

        >>> quote_domain('example.com/test')
        'example.com%2Ftest'

        >>> quote_domain('example.com?query=1')
        'example.com%3Fquery%3D1'
    '''
    ## __HAS_TEST__

    return quote(domain, safe='')

def unquote_domain(domain: str) -> str:
    '''
    Decodes a percent-encoded domain name.

    This function takes a domain name that may contain percent-encoded characters
    and returns the decoded version of the domain name.

    Args:
        domain (str): The percent-encoded domain name to decode.

    Returns:
        str: The decoded domain name.

    Examples:
        >>> unquote_domain('example%2Ecom')
        'example.com'

        >>> unquote_domain('subdomain%2Eexample%2Ecom')
        'subdomain.example.com'
    '''
    ## __HAS_TEST__

    return unquote(domain)

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def create_date_range(start_date: str = None, end_date: str = None) -> List[str]:
    '''
    Generates a list of dates between the given start_date and end_date, inclusive.

    Args:
        start_date (str, optional): The start date in 'YYYY-MM-DD' format. Defaults to None.
        end_date (str, optional): The end date in 'YYYY-MM-DD' format. Defaults to None.

    Returns:
        list: A list of date strings in 'YYYY-MM-DD' format.

    Examples:
        >>> create_date_range('2023-01-01', '2023-01-03')
        ['2023-01-01', '2023-01-02', '2023-01-03']

        >>> create_date_range('2023-01-01')
        ['2023-01-01']

        >>> create_date_range(end_date='2023-01-01')
        ['2023-01-01']

        >>> create_date_range()
        []
    '''
    ## __HAS_TEST__
    ## __BY_AI__ optimized by copilot

    ## 'or' was replaced with 'and' on 2024-01-22
    ## haven't checked if that's ok
    if not start_date and not end_date:
        return []

    if start_date and not end_date:
        return [start_date]

    if not start_date and end_date:
        return [end_date]

    ## using try block because
    ## if start_date or end_date is '2023-02-30'
    ## we will see the error:
    ## ValueError: day is out of range for month
    try:
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
        end_date_obj   = datetime.strptime(end_date,   '%Y-%m-%d')

        return [
            (start_date_obj + timedelta(days=n)).strftime('%Y-%m-%d')
            for n in range((end_date_obj - start_date_obj).days + 1)
        ]
    except Exception:
        return []

def pick_middle_item_of_list(items: List[Any]) -> Any:
    '''
    Returns the middle item of a list. If the list is empty, returns None.

    Parameters:
        items (list): The list from which to pick the middle item.

    Returns:
        object: The middle item of the list, or None if the list is empty.
    '''
    ## __HAS_TEST__
    ## __BY_AI__ optimized by copilot

    if not items:
        return None

    return items[(len(items) - 1) // 2]

def get_field_headers_and_values(request: HttpRequest, db_headers: List[str]) -> Dict[str, List[str]]:
    '''
    Extracts and processes field headers and their corresponding values from a Django request.

    Args:
        request (HttpRequest): The Django request object containing the input data.
        db_headers (List[str]): A list of database headers to extract values for.

    Returns:
        Dict[str, List[str]]: A dictionary where keys are database headers and values are lists of processed input values.

    Example:
        request = HttpRequest()
        request.GET['header1'] = 'value1||value2'
        request.GET['header2'] = 'value3'
        db_headers = ['header1', 'header2']

        result = get_field_headers_and_values(request, db_headers)
        # result will be:
        # {
        #     'header1': ['value1', 'value2'],
        #     'header2': ['value3']
        # }
    '''
    ## __HAS_TEST__

    _field_headers_and_values = {}
    for d_h in db_headers:
        input_value = get_to_shows(request, d_h)  ## string OR string1||string2
        if input_value:
            ## a. turn input_value into list by SEARCH_SIGNS.field_separator
            ## b. strip each item
            _field_headers_and_values[d_h] = list(map(lambda _: _.strip(), input_value.split(SEARCH_SIGNS.field_separator)))
    return _field_headers_and_values

def get_to_shows(
    request: HttpRequest,
    *args: str,
    pick_lowest: bool = False,
    pick_middle: bool = False,
    pick_highest: bool = False,
) -> Union[List[Any], Any, None]:
    '''
    Extracts and processes parameters from a Django request object based on the provided arguments.

    Args:
        request (HttpRequest): The Django request object containing GET or POST data.
        *args (str): Variable length argument list of parameter names to extract from the request.
        pick_lowest (bool, optional): If True, selects the lowest value from a predefined list for 'limit'. Defaults to False.
        pick_middle (bool, optional): If True, selects the middle value from a predefined list for 'limit'. Defaults to False.
        pick_highest (bool, optional): If True, selects the highest value from a predefined list for 'limit'. Defaults to False.

    Returns:
        list or single value: A list of processed parameter values or a single value if only one parameter is requested.

    Examples:
        >>> request = HttpRequest()
        >>> request.method = 'GET'
        >>> request.GET = {'page': '2', 'limit': '10'}
        >>> get_to_shows(request, 'page', 'limit')
        [2, 10]

        >>> request = HttpRequest()
        >>> request.method = 'POST'
        >>> request.POST = {'refresh': 'true', 'top': '5'}
        >>> get_to_shows(request, 'refresh', 'top')
        [True, 5]

        >>> request = HttpRequest()
        >>> request.method = 'GET'
        >>> request.GET = {'logical-operator': 'AND'}
        >>> get_to_shows(request, 'logical-operator')
        'AND'
    '''
    ## __HAS_TEST__

    if request.method == 'GET':
        _params_container = request.GET
    elif request.method == 'POST':
        _params_container = request.POST
    else:
        return None

    to_shows = []

    for arg in args:

        ## we get latest-id only from cookie
        if arg == 'latest-id':
            to_shows.append(int(request.COOKIES.get('latest-id', 0)))
            continue

        to_show = convert_string_True_False_None_0(
            _params_container.get(arg, '').strip()
        )

        added = False
        if to_show:
            if arg in ['page', 'limit', 'refresh', 'top', 'last-line']:
                try:
                    to_shows.append(abs(int(to_show)))
                    added = True
                except Exception:
                    pass
            elif arg in ['from-dropdown', 'match-case', 'overview']:
                to_shows.append(to_show in ON_TRUE)  ## True/False
                added = True
            elif arg == 'logical-operator':
                to_show = to_show.upper()
                if to_show not in LOGICAL_OPERATORS.values:
                    to_shows.append(LOGICAL_OPERATORS.default)
                else:
                    to_shows.append(to_show)
                added = True
            elif arg == 'recent':
                if to_show == PICK_AN_OPTION:
                    to_shows.append(None)
                else:
                    to_shows.append(to_show)
                added = True
            elif arg in ['time', 'time-end']:
                ## because time and time-end may be passed as hh:mm:ss
                ## for example from action_menus
                to_shows.append(to_show[:5])  ## 00:11:22 -> 00:11
                added = True
            else:
                to_shows.append(to_show)
                added = True

        if not added:
            if arg == 'page':
                to_shows.append(1)
            elif arg == 'limit':
                if pick_lowest:
                    to_shows.append(LIMITS.values[0])
                elif pick_middle:
                    to_shows.append(pick_middle_item_of_list(LIMITS.values))
                elif pick_highest:
                    to_shows.append(LIMITS.values[-1])
                else:
                    to_shows.append(LIMITS.default)
            elif arg == 'refresh':
                to_shows.append(REFRESHES.default)
            elif arg == 'top':
                to_shows.append(TOPS_TO_SHOW.default)
            elif arg == 'last-line':
                to_shows.append(LAST_LINES.default)
            elif arg == 'chosen-sensor-name':
                to_shows.append(
                    convert_string_True_False_None_0(  ## because it may be 'None'
                        request.COOKIES.get('chosensensorname')
                    )
                )
            elif arg in ['from-dropdown', 'match-case', 'overview']:
                to_shows.append(False)
            elif arg == 'logical-operator':
                to_shows.append(LOGICAL_OPERATORS.default)
            elif arg == 'recent':
                to_shows.append(None)
            elif arg in ['time', 'time-end']:
                to_shows.append(None)
            else:
                to_shows.append(None)

    if len(to_shows) == 1:
        return to_shows[0]

    return to_shows

## __NO_CACHE__
def get_dts_and_dets_from_rts(rcnt: str) -> tuple[Union[str, None], Union[str, None]]:
    '''
    Get start and end dates based on the given range context (rcnt).

    This function calculates the start and end dates for a given range context.
    The range context can be a month, a season, or a specific time period such as a week, month, or year.

    Args:
        rcnt (str): The range context. It can be a month name, a season name, or a specific time period.

    Returns:
        tuple: A tuple containing the start date and end date in the format 'YYYY-MM-DD'.
               Returns (None, None) if the range context is not recognized.

    Examples:
        >>> get_dts_and_dets_from_rts('January')
        ('2023-01-01', '2023-01-31')

        >>> get_dts_and_dets_from_rts('Spring')
        ('2023-04-01', '2023-06-30')

        >>> get_dts_and_dets_from_rts('Week')
        ('2023-10-01', '2023-10-08')

        >>> get_dts_and_dets_from_rts('2 Months')
        ('2023-08-01', '2023-10-01')

        >>> get_dts_and_dets_from_rts('Invalid')
        (None, None)
    '''
    ## __HAS_TEST__
    ## __BY_AI__ optimized by copilot

    now = datetime.now()
    y = now.year  ## 2023

    if rcnt in MONTHS_LIST:
        m = MONTHS_LIST.index(rcnt) + 1  ## 2 (number of month in calendar)
        length_of_month = monthrange(y, m)[-1]  ## 31 (JUMP_2)
        startdate = date(y, m, 1)
        enddate = date(y, m, length_of_month)
    elif rcnt in SEASONS_LIST:
        season_months = {
            'Spring': (4, 6),
            'Summer': (7, 9),
            'Fall': (10, 12),
            'Winter': (1, 3)
        }
        month_1, month_3 = season_months[rcnt]
        length_of_month_3 = monthrange(y, month_3)[-1]  ## 31 (JUMP_2)
        startdate = date(y, month_1, 1)
        enddate = date(y, month_3, length_of_month_3)
    else:
        today = date.today()
        delta_mapping = {
            ## NOTE numbers also used in base/tests/test_utils.py
            'Week':     7,
            '2 Weeks':  14,
            '3 Weeks':  21,
            'Month':    30,
            '2 Months': 60,
            '3 Months': 90,
            '6 Months': 180,
            '9 Months': 270,
            'Year':     365,
            '2 Years':  730,
        }
        if rcnt in delta_mapping:
            startdate = today - timedelta(days=delta_mapping[rcnt])
            enddate = today
        else:
            return (None, None)

    return (
        startdate.strftime('%Y-%m-%d'),
        enddate.strftime('%Y-%m-%d'),
    )

## __NO_CACHE__
def get_rts_dts_dets(
    recent_to_show: Union[str, None],
    date_to_show: Union[str, None],
    date_end_to_show: Union[str, None]
) -> tuple[Union[str, None], Union[str, None], Union[str, None]]:
    '''
    Determines the appropriate recent_to_show, date_to_show, and date_end_to_show values based on the provided inputs.

    Args:
        recent_to_show (Union[str, None]): A string indicating the recent period to show, e.g. 'Week', '2 Weeks', etc.
        date_to_show (Union[str, None]): A string representing the start date in 'YYYY-MM-DD' format.
        date_end_to_show (Union[str, None]): A string representing the end date in 'YYYY-MM-DD' format.

    Returns:
        tuple: A tuple containing the updated values of (recent_to_show, date_to_show, date_end_to_show).

    Examples:
        >>> get_rts_dts_dets('Week', None, None)
        ('Week', '2023-10-01', '2023-10-07')

        >>> get_rts_dts_dets(None, '2023-10-01', '2023-10-07')
        (None, '2023-10-01', '2023-10-07')

        >>> get_rts_dts_dets(None, '2023-10-07', '2023-10-01')
        (None, '2023-10-07', None)

        >>> get_rts_dts_dets(None, '2023-10-01', None)
        (None, '2023-10-01', None)

        >>> get_rts_dts_dets(None, None, '2023-10-07')
        (None, '2023-10-07', None)

        >>> get_rts_dts_dets(None, None, None)
        ('2 Weeks', '2023-10-01', '2023-10-07')
    '''
    ## __HAS_TEST__

    if recent_to_show:
        date_to_show, date_end_to_show = get_dts_and_dets_from_rts(rcnt=recent_to_show)
    else:
        if date_to_show and date_end_to_show:
            ## make sure date_to_show precedes date_end_to_show in time
            if date_to_show >= date_end_to_show:
                date_end_to_show = None
        elif date_to_show and not date_end_to_show:
            pass
        elif not date_to_show and date_end_to_show:
            date_to_show, date_end_to_show = date_end_to_show, None
        elif not date_to_show and not date_end_to_show:
            recent_to_show = RECENTS_TO_SHOW.default
            date_to_show, date_end_to_show = get_dts_and_dets_from_rts(rcnt=recent_to_show)

    return (recent_to_show, date_to_show, date_end_to_show)

def get_time_condition(
    str_to_be_searched: str,
    time_to_show: str,
    time_end_to_show: str
) -> tuple[str, list[str]]:
    '''
    Generates a SQL time condition based on the provided time range.

    Args:
        str_to_be_searched (str): The string to be searched in the SQL query.
        time_to_show (str): The start time in HH:MM format.
        time_end_to_show (str): The end time in HH:MM format.

    Returns:
        tuple: A tuple containing the SQL condition string and a list of time parameters.

    Examples:
        >>> get_time_condition('search_string', '02:00', '04:00')
        ('AND (Time BETWEEN %s AND %s)', ['02:00:00', '04:00:59'])

        >>> get_time_condition('', '02:00', '04:00')
        ('WHERE (Time BETWEEN %s AND %s)', ['02:00:00', '04:00:59'])

        >>> get_time_condition('search_string', '02:00', '')
        ('AND (Time BETWEEN %s AND %s)', ['02:00:00', '02:00:59'])

        >>> get_time_condition('search_string', '', '04:00')
        ('AND (Time BETWEEN %s AND %s)', ['00:00:00', '04:00:59'])

        >>> get_time_condition('', '', '')
        ('', [])
    '''
    ## __HAS_TEST__
    ## __BY_AI__ optimized by copilot

    if not time_to_show and not time_end_to_show:
        ## do NOT '' -> None
        ## we should return empty string
        return '', []

    if time_to_show and time_end_to_show:
        left_hms  = f'{time_to_show}:00'      ## 02:00:00
        right_hms = f'{time_end_to_show}:59'  ## 04:00:59
    elif time_to_show and not time_end_to_show:
        left_hms  = f'{time_to_show}:00'  ## 02:00:00
        right_hms = f'{time_to_show}:59'  ## 02:00:59
    elif not time_to_show and time_end_to_show:
        left_hms  = '00:00:00'
        right_hms = f'{time_end_to_show}:59'  ## 04:00:59
    #     ...

    if str_to_be_searched:
        return 'AND (Time BETWEEN %s AND %s)', [left_hms, right_hms]

    return 'WHERE (Time BETWEEN %s AND %s)', [left_hms, right_hms]

def get_id_condition(
    str_to_be_searched: str,
    latest_id: int,
    newest_on_top: bool = False,
    refresh_to_show: bool = False
) -> str:
    '''
    Generates a SQL condition string based on the provided parameters.

    Args:
        str_to_be_searched (str): The string to be searched in the database.
        latest_id (int): The latest ID to be used in the condition.
        newest_on_top (bool, optional): If True, IDs decrease as we scroll down. Defaults to False.
        refresh_to_show (bool, optional): If True, IDs increase at every refresh. Defaults to False.

    Returns:
        str: A SQL condition string that can be used in a WHERE or AND clause.
    '''
    ## __HAS_TEST__

    if not latest_id:
        ## do NOT '' -> None
        ## we should return empty string
        return ''

    if newest_on_top and not refresh_to_show:
        ## IDs decrease as we scroll down
        operator = '<'
    else:
        ## IDs increase as we scroll down or at every refresh
        operator = '>'

    if str_to_be_searched:
        return f'AND (ID {operator} {latest_id})'

    return f'WHERE (ID {operator} {latest_id})'

def lookup_fullinfo(ip_or_domain: str, timeout: float) -> Dict[str, Any]:
    '''
    Retrieves detailed information about an IP address or domain.

    Args:
        ip_or_domain (str): The IP address or domain to look up.
        timeout (float): The timeout duration for the HTTP request.

    Returns:
        dict: A dictionary containing detailed information about the IP address or domain.

    Raises:
        httpx.HTTPStatusError: If the HTTP request returns an unsuccessful status code.
        Exception: For any other exceptions that may occur during the request.

    Note:
        - This function uses the `httpx` library to perform the HTTP request.
    '''
    ## __HAS_TEST__

    httpx_timeout = httpx.Timeout(timeout)

    ## using try because it may sometimes
    ## throw ConnectTimeoutError
    try:
        with httpx.Client() as client:
            response = client.get(
                ## no trailing /
                url=f'{settings.LOOKUP_URL}/json/{ip_or_domain}',

                headers=HTTP_HEADERS,
                timeout=httpx_timeout,
            )

            response.raise_for_status()

            response_text = response.text
            response_dict = loads(response_text)

            return response_dict
            ## {
            ##     'ipVersion': 4,
            ##     'ipAddress': '*******',
            ##     'latitude': 37.386051,
            ##     'longitude': -122.083847,
            ##     'countryName': 'United States of America',
            ##     'countryCode': 'US',
            ##     'timeZone': '-08:00',
            ##     'zipCode': '94035',
            ##     'cityName': 'Mountain View',
            ##     'regionName': 'California',
            ##     'isProxy': False,
            ##     'continent': 'Americas',
            ##     'continentCode': 'AM'
            ## }

    except httpx.HTTPStatusError:  # as exc:
        # response_status_code = exc.response.status_code
        pass

    except Exception:
        pass

    return {}

def centralize_text(text: str) -> str:
    '''
    Centers the given text within the terminal width.

    This function calculates the number of blank spaces needed to center the text
    within the current terminal width. If the text is too long to be centered
    (i.e., the number of blanks is less than 2), it returns the original text.

    Args:
        text (str): The text to be centered.

    Returns:
        str: The centered text with leading spaces.
    '''
    ## __HAS_TEST__

    blanks = get_terminal_width() - len(text)

    if blanks < 2:
        return text

    left_margin = int(blanks / 2)
    return f'{" ":>{left_margin}}{text}'

def end_of_command_msg(self, command: str) -> str:
    '''
    This function creates a message indicating the end of a specified Django custom command,
    centralizes the message text, and applies colorization for better readability.
    Generates a formatted end-of-command message for a given Django custom command.

    Args:
        self: The instance of Command class in the Django custom command.
        command (str): The name of the Django custom command.

    Returns:
        str: A colorized and centralized end-of-command message.
    '''
    ## __HAS_TEST__

    msg = f'>>> END of {command} <<<'
    msg_centralized = centralize_text(msg)
    return colorize(self, 'command', f'{msg_centralized}\n')

def source_log_info_line(source_log: str, source_log_index: int, source_logs_len: int) -> str:
    '''
    Generates a formatted string containing information about a source log.

    Args:
        source_log (str): The path or identifier of the source log.
        source_log_index (int): The current index of the source log in the list of source logs.
        source_logs_len (int): The total number of source logs.

    Returns:
        str: A formatted string with the source log information, including its size and progress if there are multiple logs.
    '''
    ## __HAS_TEST__

    left_msg = f'source log: {to_tilda(source_log)}'

    if source_logs_len == 1:
        right_msg = convert_byte(get_size_of_source_log(source_log))
    else:
        right_msg = f'{convert_byte(get_size_of_source_log(source_log))} - {source_log_index}/{source_logs_len:,} {int((source_log_index * 100) / source_logs_len)}%'

    gap_len = get_terminal_width() - len(left_msg) - len(right_msg)

    return f'{left_msg}{" ":>{gap_len}}{right_msg}'

def get_terminal_width() -> int:
    ## __HAS_TEST__

    return int(get_terminal_size()[0])

def get_sum_of_values(dictionary: Dict[Any, int]) -> int:
    ## __HAS_TEST__

    return sum(dictionary.values())

def separator() -> str:
    ## __HAS_TEST__

    return '-' * get_terminal_width()

def trim_dict(dictionary: Dict[Any, Any], limit: int) -> Dict[Any, Any]:
    '''
    Trims a dictionary to a specified number of items.

    Parameters:
        dictionary (dict): The dictionary to be trimmed.
        limit (int): The maximum number of items to retain in the dictionary.

    Returns:
        dict: A new dictionary containing only the first 'limit' items from the original dictionary.
    '''
    ## __HAS_TEST__

    return dict(islice(dictionary.items(), limit))

def tail_file(file_path: str, num_of_lines: int) -> List[str]:
    '''
    Reads the last `num_of_lines` lines from a file.

    Args:
        file_path (str): The path to the file to read from.
        num_of_lines (int): The number of lines to read from the end of the file.

    Returns:
        List[str]: A list of the last `num_of_lines` lines from the file, in reverse order.

    Note:
        - this function was benchmarked
          and was shown to be ~98% faster than
          when we used shell tail command in subprocess
    '''
    ## __HAS_TEST__
    ## __BY_AI__ generated by copilot

    ## open in binary mode to handle file pointer correctly
    with open(file_path, 'rb') as file:
        ## seek to end of file
        file.seek(0, SEEK_END)
        file_size = file.tell()
        buffer = bytearray()
        lines = []

        ## read backwards until we have
        ## the desired number of lines
        while len(lines) <= num_of_lines and file_size > 0:
            buffer_size = min(1024, file_size)
            file_size -= buffer_size
            file.seek(file_size)
            buffer.extend(file.read(buffer_size))
            lines = buffer.split(b'\n')

        ## get last n lines
        lines = lines[-num_of_lines:]

        return list(reversed([
            _.decode('utf-8').strip()
            for _ in lines
        ]))

def strip_protocol_and_path_from_url(url: str) -> str:
    '''
    Strips the protocol (http or https) and path from a given URL, leaving only the domain.

    Args:
        url (str): The URL to be processed.

    Returns:
        str: The domain of the URL without the protocol and path.

    Examples:
        >>> strip_protocol_and_path_from_url('https://example.com/path/to/resource?query=param')
        'example.com'

        >>> strip_protocol_and_path_from_url('http://example.com')
        'example.com'

        >>> strip_protocol_and_path_from_url('https://example.com/')
        'example.com'

        >>> strip_protocol_and_path_from_url('http://example.com?query=param')
        'example.com'

    Note:
        - https://stackoverflow.com/a/69572844/
    '''
    ## __HAS_TEST__

    if url.startswith('https://'):
        url = url.partition('https://')[-1]
    elif url.startswith('http://'):
        url = url.partition('http://')[-1]

    ## remove ?one=true&two=false
    url = url.partition('?')[0]

    ## remove /a/b/c
    url = url.partition('/')[0]

    url = url.rstrip('/')

    return url

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def normalize_dns_question_name(url: str) -> str:
    '''
    Normalize a DNS question name by replacing all occurrences of numbers
    enclosed in parentheses with dots and removing any leading or trailing dots.

    Args:
        url (str): The DNS question name to be normalized.

    Returns:
        str: The normalized DNS question name.

    Examples:
        >>> normalize_dns_question_name('example(123)com')
        'example.com'

        >>> normalize_dns_question_name('(456)example(789)com')
        'example.com'

        >>> normalize_dns_question_name('example(123)com(456)')
        'example.com'

        >>> normalize_dns_question_name('sub(1)domain(2)example(3)com')
        'sub.domain.example.com'

        >>> normalize_dns_question_name('example.com')
        'example.com'

        >>> normalize_dns_question_name('')
        ''

        >>> normalize_dns_question_name('(123)(456)(789)')
        ''
    '''
    ## __HAS_TEST__
    ## __BY_AI__ optimized by copilot

    return sub(r'\([0-9]+\)', '.', url).strip('.')

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def normalize_date(date: str) -> str:
    '''
    Normalize a date string to the format 'YYYY-MM-DD'.

    This function attempts to parse the input date string using the formats
    '%m/%d/%Y' and '%m/%d/%y'. If parsing is successful, it returns the date
    in the 'YYYY-MM-DD' format. If parsing fails for all formats, it returns
    the original date string.

    Args:
        date (str): The date string to be normalized.

    Returns:
        str: The normalized date string in 'YYYY-MM-DD' format, or the original
             date string if parsing fails.

    Examples:
        >>> normalize_date('12/8/2020')
        '2020-12-08'

        >>> normalize_date('12/31/2020')
        '2020-12-31'

        >>> normalize_date('01/01/2000')
        '2000-01-01'

        >>> normalize_date('12/31/1999')
        '1999-12-31'

        >>> normalize_date('12/8/20')
        '2020-12-08'

        >>> normalize_date('12/31/20')
        '2020-12-31'

        >>> normalize_date('01/01/00')
        '2000-01-01'

        >>> normalize_date('12/31/99')
        '1999-12-31'

        >>> normalize_date('31/12/2020')
        '31/12/2020'

        >>> normalize_date('2020-12-31')
        '2020-12-31'

        >>> normalize_date('invalid date')
        'invalid date'

        >>> normalize_date('')
        ''
    '''
    ## __HAS_TEST__
    ## __BY_AI__ optimized by copilot

    for date_format in ('%m/%d/%Y', '%m/%d/%y'):
        try:
            obj = datetime.strptime(date, date_format)
            return obj.strftime('%Y-%m-%d')
        except Exception:
            continue

    ## if all formats fail,
    ## return the original date string
    return date

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def normalize_time(time: str) -> str:
    '''
    Normalize a given time string to the 24-hour format 'HH:MM:SS'.

    This function attempts to parse the input time string using two different
    time formats: 12-hour format with AM/PM ('%I:%M:%S %p') and 24-hour format
    with AM/PM ('%H:%M:%S %p'). If the input time string matches one of these
    formats, it is converted to the 24-hour format 'HH:MM:SS'. If none of the
    formats match, the original time string is returned.

    Args:
        time (str): The time string to be normalized.

    Returns:
        str: The normalized time string in 'HH:MM:SS' format, or the original
             time string if no matching format is found.

    Examples:
        >>> normalize_time('12:00:00 AM')
        '00:00:00'

        >>> normalize_time('01:30:45 AM')
        '01:30:45'

        >>> normalize_time('11:59:59 AM')
        '11:59:59'

        >>> normalize_time('12:00:00 PM')
        '12:00:00'

        >>> normalize_time('01:30:45 PM')
        '13:30:45'

        >>> normalize_time('11:59:59 PM')
        '23:59:59'

        >>> normalize_time('1:15:30 AM')
        '01:15:30'

        >>> normalize_time('9:45:00 PM')
        '21:45:00'

        >>> normalize_time('00:00:00 AM')
        '00:00:00'

        >>> normalize_time('13:30:45 PM')
        '13:30:45'

        >>> normalize_time('25:00:00 PM')
        '25:00:00 PM'

        >>> normalize_time('12:60:00 AM')
        '12:60:00 AM'

        >>> normalize_time('not a time')
        'not a time'

        >>> normalize_time('12:00:00')
        '12:00:00'
    '''
    ## __HAS_TEST__
    ## __BY_AI__ optimized by copilot

    for time_format in ('%I:%M:%S %p', '%H:%M:%S %p'):
        try:
            obj = datetime.strptime(time, time_format)
            return obj.strftime('%H:%M:%S')
        except Exception:
            continue

    ## if all formats fail,
    ## return the original time string
    return time

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def normalize_windowsserver_category(category: str) -> str:
    '''
    Normalize a Windows Server category string by converting it to lowercase and removing non-alphanumeric characters.

    This function is useful for standardizing category names for easier comparison and processing.

    Args:
        category (str): The category string to normalize.

    Returns:
        str: The normalized category string.

    Examples:
        >>> normalize_windowsserver_category('Logon/Logoff')
        'logonlogoff'

        >>> normalize_windowsserver_category('Account Management')
        'accountmanagement'

        >>> normalize_windowsserver_category('ds-access')
        'dsaccess'
    '''
    ## __HAS_TEST__

    ## JUMP_10
    return sub('[^0-9a-zA-Z]+', '', category.lower())

def get_parsed_dirs(directory: str, reverse: bool) -> List[str]:
    '''
    Get a list of parsed directories in a given directory, sorted by natural order.

    Args:
        directory (str): The path to the directory to be parsed.
        reverse (bool): If True, the list is sorted in reverse order.

    Returns:
        list: A list of directory names that match the 'YYYY-MM-DD' format.

    Examples:
        >>> get_parsed_dirs('/path/to/directory', False)
        ['2023-05-13', '2023-05-14', '2023-05-15']

        >>> get_parsed_dirs('/path/to/directory', True)
        ['2023-05-15', '2023-05-14', '2023-05-13']

        >>> get_parsed_dirs('/non/existent/directory', False)
        []

        >>> get_parsed_dirs('/path/to/empty/directory', False)
        []
    '''
    ## __HAS_TEST__

    if not path.exists(directory):
        return []
    return [
        _ for _ in natsorted(listdir(directory), reverse=reverse)
        if is_ymd(_) and path.isdir(f'{directory}/{_}')
    ]

def list_of_tuples_to_list(lst: List[tuple]) -> List[Any]:
    '''
    Convert a list of tuples into a flat list.

    Parameters:
        lst (list of tuples): A list where each element is a tuple.

    Returns:
        list: A flat list containing all elements from the tuples.

    Examples:
        >>> list_of_tuples_to_list([(1,), (2,)])
        [1, 2]

        >>> list_of_tuples_to_list([])
        []

    Note:
        - https://stackoverflow.com/a/42990453/
        - a replacement of:
            - [_[0] for _ in lst]
            - [_[0] for _ in cur.fetchall()]
        - intended to be used only for lists
          each of whose tuples are single-itemed
          i.e. (1,) not (1, 2)
    '''
    ## __HAS_TEST__

    return list(chain(*lst))

def get_date_of_source_log(log_path: str) -> str:
    '''
    Extracts the date from the log file name.

    The log file name is expected to be in the format 'YYYY-MM-DD--Day.log'.
    This function removes the '--Day.log' part and returns only the 'YYYY-MM-DD' part.

    Args:
        log_path (str): The path to the log file.

    Returns:
        str: The date extracted from the log file name.

    Examples:
        >>> get_date_of_source_log('/FOO/BAR/BAZ/2023-05-12--Fri.log')
        '2023-05-12'
        >>> get_date_of_source_log('/var/logs/2022-11-23--Wed.log')
        '2022-11-23'
        >>> get_date_of_source_log('/logs/2021-01-01--Fri.log')
        '2021-01-01'
    '''
    ## __HAS_TEST__

    root, base = path.split(log_path)  ## PATH  2023-05-12--Fri.log
    return sub('--.*$', '', base)  ## 2023-01-09

def get_size_of_source_log(log_path: str) -> int:
    '''
    Get the size of the source log file.

    This function attempts to retrieve the size of the file located at the given log_path.
    If the file does not exist or an error occurs, it returns 0.

    Parameters:
        log_path (str): The path to the log file.

    Returns:
        int: The size of the log file in bytes, or 0 if an error occurs.

    Examples:
        >>> get_size_of_source_log('/path/to/existing/logfile.log')
        1024

        >>> get_size_of_source_log('/path/to/nonexistent/logfile.log')
        0

        >>> get_size_of_source_log('/path/to/invalid/logfile.log')
        0
    '''
    ## __HAS_TEST__

    try:
        return Path(log_path).stat().st_size  ## 496373
    except Exception:
        return 0

def get_random_wallpaper() -> str:
    ## __HAS_TEST__
    static_path: str = static('files/img-wallpapers')

    random_file: str = choice(
        ## list contains full paths
        get_list_of_files(directory=f'{settings.PROJECT_DIR}/{static_path}', extension='jpg')
    )

    base: str = path.basename(random_file)  ## 63.jpg
    return f'{static_path}/{base}'  ## /static/files/img-wallpapers/63.jpg

def get_name_of_function() -> str:
    '''
    Returns the name of the function that called this function.

    This function uses the `inspect` module to retrieve the name of the function
    that is one level up in the call stack.

    Returns:
        str: The name of the calling function.

    Examples:
        >>> def example_function():
        ...     return get_name_of_function()
        >>> example_function()
        'example_function'

        >>> def another_example():
        ...     def nested_function():
        ...         return get_name_of_function()
        ...     return nested_function()
        >>> another_example()
        'nested_function'

    Note:
        - https://stackoverflow.com/a/76741643/
    '''
    ## __HAS_TEST__

    return currentframe().f_back.f_code.co_name

def ymd_to_ym(ymd: str) -> str:
    '''
    Convert a date string from 'YYYY-MM-DD' or 'YYYY_MM_DD' format to 'YYYY-MM' or 'YYYY_MM' format respectively.

    Args:
        ymd (str): Date string in 'YYYY-MM-DD' or 'YYYY_MM_DD' format.

    Returns:
        str: Date string in 'YYYY-MM' or 'YYYY_MM' format. Returns the original string if the format is not recognized.

    Examples:
        >>> ymd_to_ym('2023-10-05')
        '2023-10'
        >>> ymd_to_ym('1999-12')
        '1999-12'
        >>> ymd_to_ym('2023_10_05')
        '2023_10'
        >>> ymd_to_ym('1999_12')
        '1999_12'
        >>> ymd_to_ym('20231005')
        '20231005'
    '''
    ## __HAS_TEST__
    ## __DATABASE_YMD_PATTERN__

    if '-' in ymd:
        return sub(
            r'^(\d{4}-\d{2})-\d{2}',
            '\\1',
            ymd
        )

    if '_' in ymd:
        return sub(
            r'^(\d{4}_\d{2})_\d{2}',
            '\\1',
            ymd
        )

    return ymd

def filter_list(
    list_of_items: List[str] = None,

    year_months: List[str] = None,
    year_month_days: List[str] = None,

    start_year_month: str = None,
    start_year_month_day: str = None,

    end_year_month: str = None,
    end_year_month_day: str = None,
) -> List[str]:
    ''' __NEEDS_DOCS__ '''
    ## __HAS_TEST__

    ## list_of_items may contain:
    ##   1. full paths (as in source_logs):
    ##      list_of_items = [
    ##          '/home/<USER>/.../2024-05-12--Fri.log',
    ##          '/home/<USER>/.../2024-05-13--Sat.log',
    ##          ...
    ##      ]
    ##   OR
    ##   2. dates (as in parsed_dirs):
    ##      list_of_items = [
    ##          '2024-05-29',
    ##          '2024-05-30',
    ##          ...
    ##      ]

    list_of_items__filtered: List[str] = []

    if not list_of_items:
        return list_of_items__filtered

    if year_months:
        for y_m in year_months:     ## y_m = 2024-02
            for loi in list_of_items:
                ## JUMP_1 loi is ymd (2024-05-12)
                if is_ymd(loi): ymd = loi
                ## JUMP_4 loi is full path (/home/<USER>/.../2024-05-12--Fri.log)
                else: ymd = get_date_of_source_log(log_path=loi)  ## /home/<USER>/.../2024-05-12--Fri.log -> 2024-05-12

                if match(f'^{y_m}', ymd):
                    list_of_items__filtered.append(loi)

        list_of_items = list_of_items__filtered

    elif year_month_days:
        for y_m_d in year_month_days:     ## y_m_d = 2024-02-01
            for loi in list_of_items:
                ## JUMP_1
                if is_ymd(loi): ymd = loi
                ## JUMP_4
                else: ymd = get_date_of_source_log(log_path=loi)  ## /home/<USER>/.../2024-05-12--Fri.log -> 2024-05-12

                if y_m_d == ymd:
                    list_of_items__filtered.append(loi)

        list_of_items = list_of_items__filtered

    ## -----------------------
    ## JUMP_5
    ## NOTE keep above JUMP_6
    elif start_year_month:
        for loi in list_of_items:
            should_add = False

            ## JUMP_1
            if is_ymd(loi): ymd = loi
            ## JUMP_4
            else: ymd = get_date_of_source_log(log_path=loi)  ## /home/<USER>/.../2024-05-12--Fri.log -> 2024-05-12

            ym = ymd_to_ym(ymd)  ## 2024-03-08 -> 2024-03
            if end_year_month:
                should_add = all([
                    ym >= start_year_month,
                    ym <= end_year_month,
                ])
            else:
                should_add = ym >= start_year_month

            if should_add:
                list_of_items__filtered.append(loi)
        list_of_items = list_of_items__filtered

    ## JUMP_6
    ## NOTE keep below JUMP_5
    ##      if we reach here, it means start_year_month is None
    elif end_year_month:
        for loi in list_of_items:
            should_add = False

            ## JUMP_1
            if is_ymd(loi): ymd = loi
            ## JUMP_4
            else: ymd = get_date_of_source_log(log_path=loi)  ## /home/<USER>/.../2024-05-12--Fri.log -> 2024-05-12

            ym = ymd_to_ym(ymd)  ## 2024-03-08 -> 2024-03
            should_add = ym <= end_year_month

            if should_add:
                list_of_items__filtered.append(loi)
        list_of_items = list_of_items__filtered

    ## -----------------------
    ## JUMP_7
    ## NOTE keep above JUMP_8
    elif start_year_month_day:
        for loi in list_of_items:
            should_add = False

            ## JUMP_1
            if is_ymd(loi): ymd = loi
            ## JUMP_4
            else: ymd = get_date_of_source_log(log_path=loi)  ## /home/<USER>/.../2024-05-12--Fri.log -> 2024-05-12

            if end_year_month_day:
                should_add = all([
                    ymd >= start_year_month_day,
                    ymd <= end_year_month_day,
                ])
            else:
                should_add = ymd >= start_year_month_day

            if should_add:
                list_of_items__filtered.append(loi)
        list_of_items = list_of_items__filtered

    ## JUMP_8
    ## NOTE keep below JUMP_7
    ##      if we reach here, it means start_year_month_day is None
    elif end_year_month_day:
        for loi in list_of_items:
            should_add = False

            ## JUMP_1
            if is_ymd(loi): ymd = loi
            ## JUMP_4
            else: ymd = get_date_of_source_log(log_path=loi)  ## /home/<USER>/.../2024-05-12--Fri.log -> 2024-05-12

            should_add = ymd <= end_year_month_day

            if should_add:
                list_of_items__filtered.append(loi)
        list_of_items = list_of_items__filtered

    return natsorted(set(list_of_items))

def filter_databases(
    databases: List[str] = None,

    year_months: List[str] = None,
    year_month_days: List[str] = None,

    start_year_month: str = None,
    start_year_month_day: str = None,

    end_year_month: str = None,
    end_year_month_day: str = None,
) -> List[str]:
    ''' __NEEDS_DOCS__ '''
    ## __HAS_TEST__
    ## __DATABASE_YMD_PATTERN__

    databases__filtered: List[str] = []

    if not databases:
        return databases__filtered

    if year_months:
        for y_m in year_months:  ## y_m = 2024_02
            for db in databases:
                ## JUMP_12
                ## we are here in this if statement
                ## because list of databases is supposed
                ## to be filtered based on date
                ## so let's skip adding databases
                ## whose names contain no date
                if db in MYSQLConfig.NON_DATED_DATABASES.value:
                    continue

                if y_m in db:
                    databases__filtered.append(db)

        databases = databases__filtered

    elif year_month_days:
        for y_m_d in year_month_days:  ## y_m_d = 2024_02_01
            for db in databases:
                ## JUMP_12
                if db in MYSQLConfig.NON_DATED_DATABASES.value:
                    continue

                if y_m_d in db:
                    databases__filtered.append(db)

        databases = databases__filtered

    ## -----------------------
    ## JUMP_5
    ## NOTE keep above JUMP_6
    elif start_year_month:
        for db in databases:
            ## JUMP_12
            if db in MYSQLConfig.NON_DATED_DATABASES.value:
                continue

            should_add = False

            slug, object_name, ymd = break_name_of_database(db)  ## ..., ..., 2024_03_08
            ym = ymd_to_ym(ymd)  ## 2024_03

            if end_year_month:
                should_add = all([
                    ym >= start_year_month,
                    ym <= end_year_month,
                ])
            else:
                should_add = ym >= start_year_month

            if should_add:
                databases__filtered.append(db)
        databases = databases__filtered

    ## JUMP_6
    ## NOTE keep below JUMP_5
    ##      if we reach here, it means start_year_month is None
    elif end_year_month:
        for db in databases:
            ## JUMP_12
            if db in MYSQLConfig.NON_DATED_DATABASES.value:
                continue

            should_add = False

            slug, object_name, ymd = break_name_of_database(db)  ## ..., ..., 2024_03_08
            ym = ymd_to_ym(ymd)  ## 2024_03

            should_add = ym <= end_year_month

            if should_add:
                databases__filtered.append(db)
        databases = databases__filtered

    ## -----------------------
    ## JUMP_7
    ## NOTE keep above JUMP_8
    elif start_year_month_day:
        for db in databases:
            ## JUMP_12
            if db in MYSQLConfig.NON_DATED_DATABASES.value:
                continue

            should_add = False

            slug, object_name, ymd = break_name_of_database(db)  ## ..., ..., 2024_03_08

            if end_year_month_day:
                should_add = all([
                    ymd >= start_year_month_day,
                    ymd <= end_year_month_day,
                ])
            else:
                should_add = ymd >= start_year_month_day

            if should_add:
                databases__filtered.append(db)
        databases = databases__filtered

    ## JUMP_8
    ## NOTE keep below JUMP_7
    ##      if we reach here, it means start_year_month_day is None
    elif end_year_month_day:
        for db in databases:
            ## JUMP_12
            if db in MYSQLConfig.NON_DATED_DATABASES.value:
                continue

            should_add = False

            slug, object_name, ymd = break_name_of_database(db)  ## ..., ..., 2024_03_08

            should_add = ymd <= end_year_month_day

            if should_add:
                databases__filtered.append(db)
        databases = databases__filtered

    return natsorted(set(databases))

def reverse_date_range(date_range: List[str]) -> List[str]:
    '''
    Reverses the order of dates in a given date range.

    Args:
        date_range (list): A list of dates.

    Returns:
        list: A list of dates in reverse order.

    Examples:
        >>> reverse_date_range(['2023-01-01', '2023-01-02', '2023-01-03'])
        ['2023-01-03', '2023-01-02', '2023-01-01']

        >>> reverse_date_range([])
        []
    '''
    ## __HAS_TEST__

    return list(reversed(date_range))

def remove_trailing_slash(string: str) -> str:
    ## __HAS_TEST__

    return string.rstrip('/')

def remove_id_column(items: Union[List[Any], Tuple[Any, ...]]) -> Union[List[Any], Tuple[Any, ...]]:
    '''
    Remove the first element from a list or tuple.

    This function is typically used to remove the 'ID' column from a database header or row.

    Parameters:
        items (list or tuple): The input list or tuple from which the first element should be removed.

    Returns:
        list or tuple: The input list or tuple without the first element.

    Examples:
        >>> remove_id_column(['ID', 'Date', 'Time', 'Event', ...])
        ['Date', 'Time', 'Event', ...]

        >>> remove_id_column((12, '2024-07-01', '00:00:36', '(daemon/notice)', ...))
        ('2024-07-01', '00:00:36', '(daemon/notice)', ...)
    '''
    ## __HAS_TEST__

    return items[1:]

def dns_resolver_is_private_ip() -> bool:
    '''
    Check if the DNS resolver is using a private IP address.

    This function checks the system's DNS resolver configuration to determine if it is using a private IP address.
    It skips the check if the application is running in development mode (when `settings.DEBUG` is `True`).

    Returns:
        bool: True if the DNS resolver is using a private IP address or if in development mode, False otherwise.
    '''
    ## __HAS_TEST__

    ## skip checking on development
    if settings.DEBUG:
        return True

    with open('/etc/resolv.conf') as lines:
        for line in lines:
            line = line.strip().lower()

            if not line or \
               '#' in line or \
               'nameserver' not in line:
                continue

            if not is_private(line.split()[1]):
                return False

    return True

def command_instance_is_running(name: str) -> bool:
    '''
    Check if an instance of a Django custom command is already running.

    This function uses the `pgrep` command to check if there are multiple instances
    of a given Django custom command running. It assumes that the `BINARY_PATHS.pgrep`
    is set to the full path of the `pgrep` binary.

    Args:
        name (str): The name of the Django custom command to check.

    Returns:
        bool: True if more than one instance of the command is running, False otherwise.
    '''
    ## __HAS_TEST__

    output = run(
        ## __NEEDS_FULL_PATH__ using full path just in case
        f'{BINARY_PATHS.pgrep} -f "{name}"',
        shell=True,
        universal_newlines=True,
        capture_output=True,
    )

    ## in this function, returncode is always 0
    ## (i.e. pgrep finds an instances)
    ## because the pid of the command calling this function
    ## is also included in the stdout.
    ## so it won't help us here
    # returncode = output.returncode

    ## if len of stdout is 1, it means
    ## the command calling this function
    ## is the only instance running.
    ## if it is higher than 1, it means
    ## other instance(s) are running too
    pids = output.stdout.strip().split('\n')
    ## [422177]
    ## OR
    ## [422177, 422554]

    return len(pids) > 1

def service_is_running(name: str) -> bool:
    '''
    Check if a service is running by its name.

    This function checks if a specified service is running by executing a shell command.
    It uses full paths for the binaries because it is also run by Django, which does not have knowledge of shell paths.

    Args:
        name (str): The name of the service to check.

    Returns:
        bool: True if the service is running, False otherwise.
    '''
    ## __HAS_TEST__

    ## skip checking on development
    if settings.DEBUG:
        return True

    ## returncode   0 means cmd was     successful
    ## returncode > 0 means cmd was not successful
    return run(
        ## __NEEDS_FULL_PATH__
        ## this shell command uses full path(s)
        ## (i.e. /usr/bin/sudo instead of just sudo)
        ## because it is (also) used/run by django
        ## (when user is visiting pages on browser)
        ## and django has no idea about shell paths
        ## and about where binaries are loacted
        f'{BINARY_PATHS.sudo} {BINARY_PATHS.service} {name} status',
        shell=True,
        universal_newlines=True,
        capture_output=True,
    ).returncode == 0

def hms_to_hourkey(hms: str) -> str:
    '''
    Convert a time string in HH:MM:SS format to an hour key string.

    Args:
        hms (str): A time string in the format 'HH:MM:SS'.

    Returns:
        str: A string representing the hour range in the format 'HH:00 - HH:59'.

    Examples:
        >>> hms_to_hourkey('12:34:56')
        '12:00 - 12:59'
        >>> hms_to_hourkey('00:00:00')
        '00:00 - 00:59'
        >>> hms_to_hourkey('23:59:59')
        '23:00 - 23:59'
    '''
    ## __HAS_TEST__

    hh, mm, ss = hms.split(':')
    return f'{hh}:00 - {hh}:59'

def create_warning_message(
    request: HttpRequest,
    app_title: str,
    ymd: str,
    name_of_function: str,
    exc: Exception
) -> str:
    ## __HAS_TEST__

    msg = f'{app_title}: {ymd}'

    ## make msg verbose if user is superuser
    if request.user.is_superuser:
        msg += f' -- {name_of_function}: {exc!r}'

    return msg

def replace_ymdhms_at_beginning_of_line(ln: str, new_ymdhms: str) -> str:
    '''
    Replace the date and time at the beginning of a line with a new date and time.

    This function searches for a date and time pattern at the beginning of the given line
    and replaces it with the provided new date and time string.

    Args:
        ln (str): The input line containing the date and time to be replaced.
        new_ymdhms (str): The new date and time string to replace the old one.

    Returns:
        str: The modified line with the new date and time at the beginning.

    Examples:
        >>> replace_ymdhms_at_beginning_of_line('2023-10-01 12:34:56 Some log message', '2023-11-01 13:45:00')
        '2023-11-01 13:45:00 Some log message'

        >>> replace_ymdhms_at_beginning_of_line('2023-10-01 12:34:56 Another log entry', '2023-12-01 14:55:10')
        '2023-12-01 14:55:10 Another log entry'

        >>> replace_ymdhms_at_beginning_of_line('No date and time here', '2023-11-01 13:45:00')
        'No date and time here'
    '''
    ## __HAS_TEST__

    return sub(f'^{YMD_REGEX} {HMS_REGEX} ', f'{new_ymdhms} ', ln)

def verbose_time_to_millisecond(string: str) -> int:
    '''
    Convert a verbose time string (e.g., "1 hour 5 minutes 20 seconds") to total milliseconds.

    The input string may include any combination of hours, minutes, and seconds,
    in any of the following formats:
        - "2 hours 15 seconds"
        - "90 minutes"
        - "12 seconds"
        - "1 hour 5 minutes 20 seconds"
        - "5 minutes 0 seconds"
        - "1 hour"

    Missing components default to zero.

    Args:
        string (str): A time duration string with optional "hours", "minutes", and/or "seconds".

    Returns:
        int: The total number of milliseconds represented by the input string.

    Examples:
        >>> verbose_time_to_millisecond('1 hour 5 minutes 20 seconds')
        3920000
        >>> verbose_time_to_millisecond('90 minutes')
        5400000
        >>> verbose_time_to_millisecond('2 hours 15 seconds')
        7215000
        >>> verbose_time_to_millisecond('12 seconds')
        12000
        >>> verbose_time_to_millisecond('5 minutes')
        300000
        >>> verbose_time_to_millisecond('1 hour')
        3600000
    '''
    ## __HAS_TEST__

    ## match optional "X hours", "Y minutes", and "Z seconds" in order, with optional spaces
    pattern = r'(?:(\d+)\s*hours?)?\s*(?:(\d+)\s*minutes?)?\s*(?:(\d+)\s*seconds?)?'
    match = fullmatch(pattern, string.strip())
    if not match:
        return 0  ## OR raise ValueError("Invalid time format")

    hours   = int(match.group(1) or 0)
    minutes = int(match.group(2) or 0)
    seconds = int(match.group(3) or 0)

    return ((hours * 3600) + (minutes * 60) + seconds) * 1000

def evenly_sized_batches(total_length: int, len_of_each_batch: int = 10) -> range:
    '''
    Generate evenly sized batches from a given total length.

    Args:
        total_length (int): The total number of items to be divided into batches.
        len_of_each_batch (int, optional): The length of each batch. Defaults to 10.

    Yields:
        range: A range object representing a batch of items.

    Examples:
        # Example 1: Default batch length
        for batch in evenly_sized_batches(25):
            print(list(batch))
        # Output: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        #         [11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
        #         [21, 22, 23, 24, 25]

        # Example 2: Custom batch length
        for batch in evenly_sized_batches(25, len_of_each_batch=7):
            print(list(batch))
        # Output: [1, 2, 3, 4, 5, 6, 7]
        #         [8, 9, 10, 11, 12, 13, 14]
        #         [15, 16, 17, 18, 19, 20, 21]
        #         [22, 23, 24, 25]

    Note:
        - https://www.programiz.com/python-programming/examples/list-chunks
        - a similar structure in evenly_sized_chunks()
          in fetch-geolocation-domain.py and fetch-geolocation-ip.py
    '''
    ## __HAS_TEST__

    _rng = range(1, total_length + 1)
    for i in range(0, len(_rng), len_of_each_batch):
        yield _rng[i:i + len_of_each_batch]

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def is_invalid_log_date(log_date: str, today_ymd: str) -> bool:
    ## __HAS_TEST__

    ## when parse script starts at midnight,
    ## it should parse logs only up to the day before
    ## because today has just started (it's only 00:15 am)
    if log_date == today_ymd:
        return True

    ## there are times when logs are created
    ## which belong to dates way ahead of today's date
    ## e.g. it's 2023-01-09 today,
    ## and the name of the mistakenly-created log is 2024-10-13--Fri.log
    ## (around 9 months in future :O)
    if log_date > today_ymd:
        return True

    return False

def has_non_printable_bytes(ln):
    '''
    check if ln contains control/non-printable bytes like \x08, \x07, etc.

    Returns:
        True: for lines like:
            2021-02-23 03:54:31 WindowsServer-1 (user/notice) [MSWinEventLog	1	N/A	2605989	Mon] Jun 23 03:54:31 2025	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[dns] 6/23/2025 3:54:31 AM 1090 PACKET  00000735P95EX5A0 UDP Rcv ******* cf00   Q [0000       NOERROR]        (11)���i�=8	N/A
            2021-02-23 03:54:31 WindowsServer-1 (user/notice) [MSWinEventLog	1	N/A	2605991	Mon] Jun 23 03:54:31 2025	N/A	N/A	N/A	N/A	N/A	N/A	N/A		[dns] 6/23/2025 3:54:31 AM 1090 PACKET  00000735P95EX5A0 UDP Snd ******* cf00 R Q [0180       FORMERR]        (11)���i�=8	N/A
        False: otherwise

    Note:
        - ln does not actually contain �.
          � appears in terminal/output just due to a decoding failure,
          e.g., when reading a file with invalid bytes using the wrong encoding.
        - the '���i�=8' will be saved as '\x08\x07i=8' in mysql database.
    '''
    ## __HAS_TEST__
    ## __BY_AI__ generated by chatgpt

    ## ord() is extremely fast, C-level under the hood
    for c in ln:
        code = ord(c)
        if (code < 32 and c not in '\n\r\t') or \
           code == 127:
            return True

    return False

def get_no_of_infiles(length: int) -> int:
    '''
    Calculate the number of input files needed based on the given length.

    This function determines how many input files are required by dividing the
    length by the chunk size defined in MYSQLConfig.INFILE_CHUNKSIZE. If the length
    is not perfectly divisible by the chunk size, an additional file is accounted for.

    Args:
        length (int): The total length of the data to be divided into input files.

    Returns:
        int: The number of input files needed.

    Examples:
        >>> MYSQLConfig.INFILE_CHUNKSIZE.value = 1000
        >>> get_no_of_infiles(0)
        0

        >>> get_no_of_infiles(500)
        1

        >>> get_no_of_infiles(1000)
        1

        >>> get_no_of_infiles(1500)
        2

        >>> get_no_of_infiles(2000)
        2

        >>> get_no_of_infiles(2500)
        3
    '''
    ## __HAS_TEST__

    if length == 0:
        return 0

    if length <= MYSQLConfig.INFILE_CHUNKSIZE.value:
        return 1

    _no_of_chunks = int(length / MYSQLConfig.INFILE_CHUNKSIZE.value)

    if not length % MYSQLConfig.INFILE_CHUNKSIZE.value == 0:
        _no_of_chunks += 1

    return _no_of_chunks

def create_name_of_index(key: str) -> str:
    '''
    Generate a sanitized index name from a given key.

    This function takes a string key, converts it to lowercase, removes any non-alphanumeric characters,
    and appends '_index' to the end of the sanitized string.

    Args:
        key (str): The input string to be sanitized and converted into an index name.

    Returns:
        str: The sanitized index name.

    Examples:
        >>> create_name_of_index('Source IP')
        'sourceip_index'

        >>> create_name_of_index('Domain')
        'domain_index'

        >>> create_name_of_index('GID:SID')
        'gidsid_index'
    '''
    ## __HAS_TEST__

    ## JUMP_10
    return f"{sub('[^0-9a-zA-Z]+', '', key.lower())}_index"

def create_path_of_infile(database_name: str, table_name: str, chunk_number: int = 0) -> str:
    '''
    Generate the file path for an infile based on the database name, table name, and optional chunk number.

    Args:
        database_name (str): The name of the database.
        table_name (str): The name of the table.
        chunk_number (int, optional): The chunk number to include in the file path. Defaults to 0.

    Returns:
        str: The generated file path.

    Examples:
        >>> create_path_of_infile('my_database', 'my_table')
        '/tmp/infile__my_database__my_table.csv'

        >>> create_path_of_infile('my_database', 'my_table', 1)
        '/tmp/infile__my_database__my_table__chunk_1.csv'

        >>> create_path_of_infile('my_database', 'my_table', 42)
        '/tmp/infile__my_database__my_table__chunk_42.csv'
    '''
    ## __HAS_TEST__
    ## __DATABASE_YMD_PATTERN__

    _suff = ''

    if chunk_number:
        _suff = f'__chunk_{chunk_number}'

    return f'/tmp/infile__{database_name}__{table_name}{_suff}.csv'

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def dash_to_underscore(string: str = None) -> str:
    '''
    Converts all dashes ('-') in the given string to underscores ('_').

    Parameters:
        string (str): The input string to be converted. If None or not provided, an empty string is used.

    Returns:
        str: The converted string with dashes replaced by underscores.

    Examples:
        >>> dash_to_underscore('Sensor-One')
        'Sensor_One'
        >>> dash_to_underscore('2024-11-30')
        '2024_11_30'
    '''
    ## __HAS_TEST__

    if not string:
        string = ''

    return string.replace('-', '_')

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def underscore_to_dash(string: str = '') -> str:
    '''
    Converts all underscores ('_') in the given string to dashes ('-').

    Parameters:
        string (str): The input string to be converted. If None or not provided, an empty string is used.

    Returns:
        str: The converted string with underscores replaced by dashes.

    Examples:
        >>> underscore_to_dash('Sensor_One')
        'Sensor-One'
        >>> underscore_to_dash('2024_11_30')
        '2024-11-30'
    '''
    ## __HAS_TEST__

    return string.replace('_', '-')

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def break_name_of_database(database_name: str) -> Tuple[str, str, str]:
    '''
    Breaks down the name of a database into its components.

    Args:
        database_name (str): The name of the database to be broken down.

    Returns:
        tuple: A tuple containing the slug, object name, and date (ymd) in the format (slug, object_name, ymd).

    Examples:
        >>> break_name_of_database('daemon__Sensor_One__2024_11_30')
        ('daemon', 'Sensor_One', '2024_11_30')

        >>> break_name_of_database('dhcp__2024_11_30')
        ('dhcp', '', '2024_11_30')

        >>> break_name_of_database('geolocation')
        ('geolocation', '', '')
    '''
    ## __HAS_TEST__
    ## __DATABASE_YMD_PATTERN__

    if database_name in MYSQLConfig.NON_DATED_DATABASES.value:
        return (database_name, '', '')

    splited = database_name.split(MYSQLConfig.DB_NAME_SEPARATOR.value)

    if len(splited) == 2:
        slug        = splited[0]  ## dhcp
        object_name = ''          ## ''
        ymd         = splited[1]  ## 2024_11_30
    elif len(splited) == 3:
        slug        = splited[0]  ## daemon
        object_name = splited[1]  ## Sensor_One
        ymd         = splited[2]  ## 2024_11_30
    else:
        slug        = ''
        object_name = ''
        ymd         = ''

    return (slug, object_name, ymd)

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def get_directory_path_from_name_of_database(database_name: str) -> Union[str, None]:
    '''
    Get the directory path from the name of the database.

    This function takes a database name and returns the corresponding directory path based on predefined patterns and settings.

    Args:
        database_name (str): The name of the database.

    Returns:
        str: The directory path corresponding to the database name, or None if the slug is not found.

    Examples:
        >>> get_directory_path_from_name_of_database('geolocation')
        '/FOO/BAR/BAZ/geolocation'

        >>> get_directory_path_from_name_of_database('dhcp__2024_11_30')
        '/FOO/BAR/BAZ/dhcp/2024-11-30'

        >>> get_directory_path_from_name_of_database('daemon__Sensor_One__2024_11_30')
        '/FOO/BAR/BAZ/daemon/Sensor-One/2024-11-30'

        >>> get_directory_path_from_name_of_database('unknown_database')
        None
    '''
    ## __HAS_TEST__
    ## __DATABASE_YMD_PATTERN__

    if database_name == GeoLocationConfig.SLUG.value:
        return GeoLocationConfig.get_logs_parsed_dir()

    if database_name == MaliciousConfig.SLUG.value:
        return MaliciousConfig.get_logs_parsed_dir()

    slug, object_name, ymd = break_name_of_database(database_name)

    if not slug:
        return None

    if object_name:
        return f'{settings.LOGS_PARSED_DIR}/{slug}/{underscore_to_dash(object_name)}/{underscore_to_dash(ymd)}'

    return f'{settings.LOGS_PARSED_DIR}/{slug}/{underscore_to_dash(ymd)}'

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def create_name_of_database(slug: str, ymd: str = '', object_name: str = '') -> str:
    '''
    Generate the name of a database based on the provided slug, date, and object name.

    Args:
        slug (str): The base name of the database.
        ymd (str, optional): The date in 'YYYY-MM-DD' format. Defaults to an empty string.
        object_name (str, optional): The name of the object. Defaults to an empty string.

    Returns:
        str: The generated name of the database.

    Examples:
        >>> create_name_of_database('daemon', '2024-11-30', 'Sensor-One')
        'daemon__Sensor_One__2024_11_30'

        >>> create_name_of_database('dhcp', '2024-11-30')
        'dhcp__2024_11_30'

        >>> create_name_of_database('malicious')
        'malicious'
    '''
    ## __HAS_TEST__
    ## __DATABASE_YMD_PATTERN__

    if slug in MYSQLConfig.NON_DATED_DATABASES.value:
        return slug

    if not object_name:
        return f'{slug}{MYSQLConfig.DB_NAME_SEPARATOR.value}{dash_to_underscore(ymd)}'

    return f'{slug}{MYSQLConfig.DB_NAME_SEPARATOR.value}{dash_to_underscore(object_name)}{MYSQLConfig.DB_NAME_SEPARATOR.value}{dash_to_underscore(ymd)}'

##########################################################

## human_format
# def human_format(num):
#     '''
#     98346473 -> 98M
#     98346 -> 98K
#     '''
#     ## https://stackoverflow.com/questions/579310/formatting-long-numbers-as-strings/45846841#45846841
#
#     from re import sub
#     _magnitude = 0
#     while abs(num) >= 1000:
#         _magnitude += 1
#         num /= 1000.0
#
#     num = f'{num:.1f}'
#     num = sub(r'\.0+$', '', num)  ## 98.0 -> 98
#
#     return f"{num}{['', 'K', 'M', 'B', 'T', 'Q'][_magnitude]}"


## check_gz_integrity
"""
from gzip import open as gzip_open
import gzip
def check_gz_integrity(gz_path):
    ## https://stackoverflow.com/questions/41998226/python-checking-integrity-of-gzip-archive

    with gzip_open(gz_path, 'rb') as f:
        try:
            chunksize = 10_000_000  ## 10 Mbytes
            while f.read(chunksize) != b'':
                pass
            return True

        ## file is not a gzip file
        except gzip.BadGzipFile:
            return False

        ## EOFError: Compressed file ended before the end-of-stream marker was reached
        ## file is a truncated gzip file
        except EOFError:
            return False

        except Exception:
            return False

    return False
"""
